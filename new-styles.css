/* 4 Elements Hair - Premium Sleek Design */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap");

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Prevent horizontal scrolling */
html,
body {
  overflow-x: hidden;
  width: 100%;
}

/* Improve mobile performance */
* {
  -webkit-tap-highlight-color: transparent;
}

/* Better font rendering */
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

:root {
  /* Premium Color Palette - Inspired by reference */
  --primary-pink: #f4c2c2;
  --soft-pink: #f9e5e5;
  --blush: #f7d7d7;
  --cream: #fdf9f7;
  --charcoal: #2c2c2c;
  --dark-grey: #4a4a4a;
  --light-grey: #8b8b8b;
  --white: #ffffff;
  --accent-gold: #d4af37;

  /* Typography */
  --font-primary: "Inter", sans-serif;
  --font-display: "Playfair Display", serif;

  /* Spacing */
  --section-padding: 80px 0;
  --container-max: 1200px;

  /* Transitions */
  --transition: all 0.3s ease;
}

body {
  font-family: var(--font-primary);
  line-height: 1.6;
  color: var(--charcoal);
  background: var(--white);
  font-weight: 400;
}

.container {
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 0 20px;
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 15px 0;
  transition: var(--transition);
  border-bottom: 1px solid rgba(244, 194, 194, 0.1);
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 0 20px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo img {
  height: 100px;
  width: auto;
  object-fit: contain;
  max-width: 300px;
}

.logo-text {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  display: none; /* Hide text since logo includes text */
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 40px;
}

.nav-link {
  text-decoration: none;
  color: var(--charcoal);
  font-weight: 500;
  font-size: 15px;
  transition: var(--transition);
  position: relative;
}

.nav-link:hover {
  color: var(--primary-pink);
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-pink);
  transition: var(--transition);
}

.nav-link:hover::after {
  width: 100%;
}

.book-btn {
  background: var(--primary-pink);
  color: var(--white);
  padding: 12px 24px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  transition: var(--transition);
}

.book-btn:hover {
  background: var(--charcoal);
  transform: translateY(-2px);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 8px;
  gap: 4px;
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background: var(--charcoal);
  transition: var(--transition);
  border-radius: 2px;
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Navigation Menu */
.nav-menu.mobile-active {
  display: flex;
  position: fixed;
  top: 130px; /* Match navbar height */
  left: 0;
  right: 0;
  background: var(--white);
  flex-direction: column;
  padding: 30px 20px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  border-top: 1px solid rgba(244, 194, 194, 0.3);
  backdrop-filter: blur(10px);
  z-index: 999;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.nav-menu.mobile-active .nav-link,
.nav-menu.mobile-active .book-btn {
  margin-bottom: 20px;
  text-align: center;
  padding: 18px 20px;
  border-radius: 12px;
  transition: var(--transition);
  font-weight: 500;
  font-size: 16px;
}

.nav-menu.mobile-active .nav-link {
  color: var(--charcoal);
  border: 2px solid transparent;
}

.nav-menu.mobile-active .nav-link:hover,
.nav-menu.mobile-active .nav-link:focus {
  background: var(--soft-pink);
  border-color: var(--primary-pink);
  transform: translateY(-2px);
}

.nav-menu.mobile-active .book-btn {
  margin-bottom: 0;
  background: var(--primary-pink);
  color: var(--white);
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(244, 194, 194, 0.4);
}

.nav-menu.mobile-active .book-btn:hover {
  background: var(--charcoal);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(44, 44, 44, 0.3);
}

/* Mobile menu overlay */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 998;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-menu-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Prevent body scroll when mobile menu is open */
body.mobile-menu-open {
  overflow: hidden;
}

/* Hero Section - Premium Redesign */
.hero {
  min-height: 100vh;
  background: linear-gradient(135deg, #fdf9f7 0%, #f9e5e5 50%, #f4c2c2 100%);
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  padding-top: 130px; /* Account for fixed navbar */
}

.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="0.5" fill="%23F4C2C2" opacity="0.1"/><circle cx="80" cy="40" r="0.3" fill="%23F4C2C2" opacity="0.1"/><circle cx="40" cy="80" r="0.4" fill="%23F4C2C2" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  z-index: 1;
}

.hero-content {
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 100px;
  align-items: center;
  width: 100%;
  position: relative;
  z-index: 2;
}

.hero-text {
  position: relative;
}

.hero-text::before {
  content: "";
  position: absolute;
  top: -40px;
  left: -20px;
  width: 120px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-pink), var(--accent-gold));
  border-radius: 2px;
}

.hero-text h1 {
  font-family: var(--font-display);
  font-size: 72px;
  font-weight: 700;
  line-height: 1.05;
  color: var(--charcoal);
  margin-bottom: 32px;
  letter-spacing: -0.02em;
}

.hero-text .highlight {
  background: linear-gradient(135deg, var(--primary-pink), #e8a5a5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-style: italic;
  position: relative;
}

.hero-text .highlight::after {
  content: "";
  position: absolute;
  bottom: 8px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-pink), transparent);
  opacity: 0.3;
}

.hero-subtitle {
  font-size: 22px;
  color: var(--dark-grey);
  margin-bottom: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 14px;
}

.hero-text p {
  font-size: 20px;
  color: var(--dark-grey);
  margin-bottom: 48px;
  line-height: 1.7;
  max-width: 520px;
  font-weight: 400;
}

.hero-stats {
  display: flex;
  gap: 40px;
  margin-bottom: 48px;
}

.stat-item {
  text-align: left;
}

.stat-number {
  font-family: var(--font-display);
  font-size: 36px;
  font-weight: 700;
  color: var(--primary-pink);
  display: block;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: var(--light-grey);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 4px;
}

.hero-buttons {
  display: flex;
  gap: 20px;
  align-items: center;
}

.btn-primary {
  background: var(--charcoal);
  color: var(--white);
  padding: 18px 36px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  transition: var(--transition);
  border: 2px solid var(--charcoal);
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  background: var(--primary-pink);
  border-color: var(--primary-pink);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(244, 194, 194, 0.4);
}

.btn-secondary {
  background: transparent;
  color: var(--charcoal);
  padding: 18px 36px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  transition: var(--transition);
  border: 2px solid var(--charcoal);
  position: relative;
}

.btn-secondary:hover {
  background: var(--charcoal);
  color: var(--white);
  transform: translateY(-2px);
}

/* Secondary button on dark backgrounds */
.cta-section .btn-secondary {
  background: transparent;
  color: var(--white);
  border: 2px solid var(--white);
}

.cta-section .btn-secondary:hover {
  background: var(--white);
  color: var(--charcoal);
  transform: translateY(-2px);
}

.hero-visual {
  position: relative;
  height: 700px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-image-main {
  position: relative;
  width: 100%;
  height: 600px;
  background: linear-gradient(135deg, var(--primary-pink), #e8a5a5);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 30px 60px rgba(244, 194, 194, 0.3);
  transform: rotate(-2deg);
  transition: var(--transition);
}

.hero-image-main:hover {
  transform: rotate(0deg) scale(1.02);
}

.hero-image-main img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  border-radius: 20px;
}

.hero-image-main::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.05) 100%
  );
}

.hero-floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.floating-element {
  position: absolute;
  background: var(--white);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.floating-element-1 {
  top: 10%;
  right: -10%;
  width: 180px;
  animation: float 6s ease-in-out infinite;
}

.floating-element-2 {
  bottom: 15%;
  left: -15%;
  width: 160px;
  animation: float 8s ease-in-out infinite reverse;
}

.floating-element h4 {
  font-family: var(--font-display);
  font-size: 18px;
  color: var(--charcoal);
  margin-bottom: 8px;
}

.floating-element p {
  font-size: 14px;
  color: var(--light-grey);
  margin: 0;
}

.hero-image-placeholder {
  color: var(--white);
  text-align: center;
  z-index: 2;
  position: relative;
}

.hero-image-placeholder i {
  font-size: 100px;
  margin-bottom: 24px;
  opacity: 0.9;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.hero-image-placeholder p {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Elements Section */
.elements-section {
  padding: var(--section-padding);
  background: var(--white);
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
}

.section-title {
  font-family: var(--font-display);
  font-size: 48px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 18px;
  color: var(--light-grey);
  max-width: 600px;
  margin: 0 auto;
}

.elements-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
}

.element-card {
  text-align: center;
  padding: 40px 20px;
  border-radius: 12px;
  background: var(--white);
  border: 1px solid rgba(244, 194, 194, 0.2);
  transition: var(--transition);
}

.element-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(244, 194, 194, 0.2);
  border-color: var(--primary-pink);
}

.element-icon {
  width: 80px;
  height: 80px;
  background: var(--soft-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  transition: var(--transition);
}

.element-card:hover .element-icon {
  background: var(--primary-pink);
  transform: scale(1.1);
}

.element-icon i {
  font-size: 32px;
  color: var(--charcoal);
  transition: var(--transition);
}

.element-card:hover .element-icon i {
  color: var(--white);
}

.element-card h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.element-card p {
  color: var(--light-grey);
  font-size: 15px;
  line-height: 1.6;
}

/* Services Section */
.services-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}

.services-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.services-text h2 {
  font-family: var(--font-display);
  font-size: 48px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 24px;
  line-height: 1.2;
}

.services-text p {
  font-size: 18px;
  color: var(--dark-grey);
  margin-bottom: 32px;
  line-height: 1.7;
}

.services-list {
  list-style: none;
  margin-bottom: 40px;
}

.services-list li {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  color: var(--charcoal);
}

.services-list i {
  color: var(--primary-pink);
  margin-right: 12px;
  font-size: 18px;
}

.services-image {
  height: 500px;
  background: var(--white);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.services-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  border-radius: 12px;
}

/* Services Grid */
.services-grid-section {
  padding: var(--section-padding);
  background: var(--white);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
}

.service-card {
  background: var(--white);
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(244, 194, 194, 0.1);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-pink), var(--accent-gold));
  transform: scaleX(0);
  transition: var(--transition);
}

.service-card:hover::before {
  transform: scaleX(1);
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
  border-color: var(--primary-pink);
}

.service-card.featured {
  border: 2px solid var(--primary-pink);
  background: linear-gradient(135deg, var(--white) 0%, var(--soft-pink) 100%);
}

.service-card.featured::before {
  transform: scaleX(1);
}

.service-icon {
  width: 80px;
  height: 80px;
  background: var(--soft-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  transition: var(--transition);
}

.service-card:hover .service-icon {
  background: var(--primary-pink);
  transform: scale(1.1);
}

.service-card.featured .service-icon {
  background: var(--primary-pink);
}

.service-icon i {
  font-size: 32px;
  color: var(--charcoal);
  transition: var(--transition);
}

.service-card:hover .service-icon i,
.service-card.featured .service-icon i {
  color: var(--white);
}

.service-card h3 {
  font-family: var(--font-display);
  font-size: 28px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.service-card p {
  color: var(--dark-grey);
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 24px;
}

.service-features {
  list-style: none;
  margin-bottom: 24px;
}

.service-features li {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 15px;
  color: var(--charcoal);
}

.service-features i {
  color: var(--primary-pink);
  margin-right: 12px;
  font-size: 16px;
}

.service-price {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-pink);
  text-align: right;
}

/* Process Section */
.process-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.process-step {
  text-align: center;
  position: relative;
}

.step-number {
  width: 80px;
  height: 80px;
  background: var(--primary-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  font-family: var(--font-display);
  font-size: 32px;
  font-weight: 700;
  color: var(--white);
  box-shadow: 0 8px 20px rgba(244, 194, 194, 0.4);
}

.process-step h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.process-step p {
  color: var(--dark-grey);
  font-size: 15px;
  line-height: 1.6;
}

/* CTA Section */
.cta-section {
  padding: var(--section-padding);
  background: var(--charcoal);
  text-align: center;
}

.cta-content h2 {
  font-family: var(--font-display);
  font-size: 48px;
  font-weight: 600;
  color: var(--white);
  margin-bottom: 20px;
}

.cta-content p {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 40px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: center;
}

/* Page Header */
.page-header {
  padding: 140px 0 80px;
  background: linear-gradient(135deg, var(--soft-pink) 0%, var(--cream) 100%);
  text-align: center;
  position: relative;
}

.page-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="0.5" fill="%23F4C2C2" opacity="0.1"/><circle cx="80" cy="40" r="0.3" fill="%23F4C2C2" opacity="0.1"/><circle cx="40" cy="80" r="0.4" fill="%23F4C2C2" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.page-header-content {
  position: relative;
  z-index: 2;
}

.page-header h1 {
  font-family: var(--font-display);
  font-size: 56px;
  font-weight: 700;
  color: var(--charcoal);
  margin-bottom: 20px;
  letter-spacing: -0.02em;
}

.page-header .highlight {
  background: linear-gradient(135deg, var(--primary-pink), #e8a5a5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-style: italic;
}

.page-header p {
  font-size: 20px;
  color: var(--dark-grey);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Services Grid */
.services-grid-section {
  padding: var(--section-padding);
  background: var(--white);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
}

.service-card {
  background: var(--white);
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(244, 194, 194, 0.1);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-pink), var(--accent-gold));
  transform: scaleX(0);
  transition: var(--transition);
}

.service-card:hover::before {
  transform: scaleX(1);
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
  border-color: var(--primary-pink);
}

.service-card.featured {
  border: 2px solid var(--primary-pink);
  background: linear-gradient(135deg, var(--white) 0%, var(--soft-pink) 100%);
}

.service-card.featured::before {
  transform: scaleX(1);
}

.service-icon {
  width: 80px;
  height: 80px;
  background: var(--soft-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  transition: var(--transition);
}

.service-card:hover .service-icon {
  background: var(--primary-pink);
  transform: scale(1.1);
}

.service-card.featured .service-icon {
  background: var(--primary-pink);
}

.service-icon i {
  font-size: 32px;
  color: var(--charcoal);
  transition: var(--transition);
}

.service-card:hover .service-icon i,
.service-card.featured .service-icon i {
  color: var(--white);
}

.service-card h3 {
  font-family: var(--font-display);
  font-size: 28px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.service-card p {
  color: var(--dark-grey);
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 24px;
}

.service-features {
  list-style: none;
  margin-bottom: 24px;
}

.service-features li {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 15px;
  color: var(--charcoal);
}

.service-features i {
  color: var(--primary-pink);
  margin-right: 12px;
  font-size: 16px;
}

.service-price {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-pink);
  text-align: right;
}

/* Process Section */
.process-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.process-step {
  text-align: center;
  position: relative;
}

.step-number {
  width: 80px;
  height: 80px;
  background: var(--primary-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  font-family: var(--font-display);
  font-size: 32px;
  font-weight: 700;
  color: var(--white);
  box-shadow: 0 8px 20px rgba(244, 194, 194, 0.4);
}

.process-step h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.process-step p {
  color: var(--dark-grey);
  font-size: 15px;
  line-height: 1.6;
}

/* CTA Section */
.cta-section {
  padding: var(--section-padding);
  background: var(--charcoal);
  text-align: center;
}

.cta-content h2 {
  font-family: var(--font-display);
  font-size: 48px;
  font-weight: 600;
  color: var(--white);
  margin-bottom: 20px;
}

.cta-content p {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 40px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: center;
}

/* Active Navigation State */
.nav-link.active {
  color: var(--primary-pink);
}

.nav-link.active::after {
  width: 100%;
}

/* About Page Styles */
.about-hero {
  padding: var(--section-padding);
  background: var(--white);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: 80px;
  align-items: center;
}

.about-image {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.3);
  transition: var(--transition);
  height: 500px;
  width: 100%;
}

.about-image:hover {
  transform: translateY(-8px);
  box-shadow: 0 30px 70px rgba(244, 194, 194, 0.4);
}

.about-image img {
  width: 100%;
  height: 500px;
  object-fit: cover;
  object-position: center;
  display: block;
}

.about-text h2 {
  font-family: var(--font-display);
  font-size: 42px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 24px;
}

.about-text p {
  font-size: 18px;
  color: var(--dark-grey);
  line-height: 1.7;
  margin-bottom: 24px;
}

.credentials {
  margin-top: 40px;
}

.credential-item {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 24px;
  padding: 20px;
  background: var(--soft-pink);
  border-radius: 12px;
  transition: var(--transition);
}

.credential-item:hover {
  transform: translateX(8px);
  box-shadow: 0 8px 25px rgba(244, 194, 194, 0.2);
}

.credential-icon {
  width: 60px;
  height: 60px;
  background: var(--primary-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.credential-icon i {
  font-size: 24px;
  color: var(--white);
}

.credential-text h4 {
  font-family: var(--font-display);
  font-size: 20px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 4px;
}

.credential-text p {
  font-size: 14px;
  color: var(--light-grey);
  margin: 0;
}

.experience-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}

.experience-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.experience-card {
  text-align: center;
  padding: 40px 20px;
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: var(--transition);
}

.experience-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
}

.experience-number {
  font-family: var(--font-display);
  font-size: 48px;
  font-weight: 700;
  color: var(--primary-pink);
  display: block;
  line-height: 1;
  margin-bottom: 16px;
}

.experience-card h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 12px;
}

.experience-card p {
  color: var(--dark-grey);
  font-size: 15px;
  line-height: 1.6;
}

.philosophy-section {
  padding: var(--section-padding);
  background: var(--white);
}

.philosophy-content {
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 80px;
  align-items: center;
}

.philosophy-text h2 {
  font-family: var(--font-display);
  font-size: 42px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 24px;
}

.philosophy-text > p {
  font-size: 20px;
  color: var(--dark-grey);
  line-height: 1.7;
  margin-bottom: 40px;
  font-style: italic;
  position: relative;
  padding-left: 20px;
}

.philosophy-text > p::before {
  content: '"';
  position: absolute;
  left: 0;
  top: -10px;
  font-size: 60px;
  color: var(--primary-pink);
  font-family: var(--font-display);
}

.philosophy-points {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.philosophy-point {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.philosophy-point i {
  font-size: 24px;
  color: var(--primary-pink);
  margin-top: 4px;
}

.philosophy-point h4 {
  font-family: var(--font-display);
  font-size: 20px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 8px;
}

.philosophy-point p {
  color: var(--dark-grey);
  font-size: 15px;
  line-height: 1.6;
  margin: 0;
}

.philosophy-image {
  height: 500px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.3);
}

.philosophy-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
}

.specialties-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}

.specialties-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.specialty-card {
  background: var(--white);
  border-radius: 16px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: var(--transition);
}

.specialty-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
}

.specialty-icon {
  width: 80px;
  height: 80px;
  background: var(--primary-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
}

.specialty-icon i {
  font-size: 32px;
  color: var(--white);
}

.specialty-card h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.specialty-card p {
  color: var(--dark-grey);
  font-size: 16px;
  line-height: 1.6;
}

/* Sustainability Page Styles */
.sustainability-stats {
  padding: var(--section-padding);
  background: var(--white);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.stat-card {
  text-align: center;
  padding: 40px 20px;
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(244, 194, 194, 0.1);
  transition: var(--transition);
}

.stat-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
  border-color: var(--primary-pink);
}

.stat-icon {
  width: 80px;
  height: 80px;
  background: var(--soft-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  transition: var(--transition);
}

.stat-card:hover .stat-icon {
  background: var(--primary-pink);
  transform: scale(1.1);
}

.stat-icon i {
  font-size: 32px;
  color: var(--charcoal);
  transition: var(--transition);
}

.stat-card:hover .stat-icon i {
  color: var(--white);
}

.stat-number {
  font-family: var(--font-display);
  font-size: 48px;
  font-weight: 700;
  color: var(--primary-pink);
  display: block;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-family: var(--font-display);
  font-size: 20px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.stat-card p {
  color: var(--dark-grey);
  font-size: 15px;
  line-height: 1.6;
}

.sustainable-practices {
  padding: var(--section-padding);
  background: var(--soft-pink);
}

.practices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.practice-card {
  background: var(--white);
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: var(--transition);
}

.practice-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
}

.practice-icon {
  width: 80px;
  height: 80px;
  background: var(--primary-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
}

.practice-icon i {
  font-size: 32px;
  color: var(--white);
}

.practice-card h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.practice-card p {
  color: var(--dark-grey);
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 24px;
}

.practice-features {
  list-style: none;
}

.practice-features li {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 15px;
  color: var(--charcoal);
}

.practice-features i {
  color: var(--primary-pink);
  margin-right: 12px;
  font-size: 16px;
}

.ponytail-donation {
  padding: var(--section-padding);
  background: var(--white);
}

.donation-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.donation-text h2 {
  font-family: var(--font-display);
  font-size: 42px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 24px;
}

.donation-text p {
  font-size: 18px;
  color: var(--dark-grey);
  line-height: 1.7;
  margin-bottom: 32px;
}

.donation-requirements h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 20px;
}

.donation-requirements ul {
  list-style: none;
  margin-bottom: 32px;
}

.donation-requirements li {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 16px;
  color: var(--charcoal);
}

.donation-requirements i {
  color: var(--primary-pink);
  margin-right: 12px;
  font-size: 18px;
}

.donation-image {
  height: 400px;
  background: var(--soft-pink);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.donation-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  border-radius: 16px;
}

.partnership-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}

.partnership-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.partnership-image {
  height: 400px;
  background: var(--white);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.partnership-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  border-radius: 16px;
}

.partnership-text h2 {
  font-family: var(--font-display);
  font-size: 42px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 24px;
}

.partnership-text p {
  font-size: 18px;
  color: var(--dark-grey);
  line-height: 1.7;
  margin-bottom: 32px;
}

.partnership-benefits h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 20px;
}

.partnership-benefits ul {
  list-style: none;
  margin-bottom: 32px;
}

.partnership-benefits li {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 16px;
  color: var(--charcoal);
}

.partnership-benefits i {
  color: var(--primary-pink);
  margin-right: 12px;
  font-size: 18px;
}

/* Contact Page Styles */
.contact-info {
  padding: var(--section-padding);
  background: var(--white);
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.contact-card {
  text-align: center;
  padding: 40px 20px;
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(244, 194, 194, 0.1);
  transition: var(--transition);
}

.contact-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
  border-color: var(--primary-pink);
}

.contact-icon {
  width: 80px;
  height: 80px;
  background: var(--soft-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  transition: var(--transition);
}

.contact-card:hover .contact-icon {
  background: var(--primary-pink);
  transform: scale(1.1);
}

.contact-icon i {
  font-size: 32px;
  color: var(--charcoal);
  transition: var(--transition);
}

.contact-card:hover .contact-icon i {
  color: var(--white);
}

.contact-card h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 12px;
}

.contact-card p {
  font-size: 16px;
  color: var(--charcoal);
  margin-bottom: 8px;
  line-height: 1.5;
}

.contact-card p a {
  color: var(--primary-pink);
  text-decoration: none;
  font-weight: 600;
}

.contact-card p a:hover {
  text-decoration: underline;
}

.contact-card span {
  font-size: 14px;
  color: var(--light-grey);
}

.booking-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}

.booking-content {
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 80px;
  align-items: center;
}

.booking-text h2 {
  font-family: var(--font-display);
  font-size: 42px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 24px;
}

.booking-text > p {
  font-size: 18px;
  color: var(--dark-grey);
  line-height: 1.7;
  margin-bottom: 40px;
}

.booking-benefits {
  margin-bottom: 40px;
}

.booking-benefit {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 24px;
}

.booking-benefit i {
  font-size: 24px;
  color: var(--primary-pink);
  margin-top: 4px;
}

.booking-benefit h4 {
  font-family: var(--font-display);
  font-size: 20px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 8px;
}

.booking-benefit p {
  color: var(--dark-grey);
  font-size: 15px;
  line-height: 1.6;
  margin: 0;
}

.booking-cta {
  background: var(--white);
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

.booking-cta h3 {
  font-family: var(--font-display);
  font-size: 28px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 12px;
}

.booking-cta p {
  font-size: 16px;
  color: var(--dark-grey);
  margin-bottom: 24px;
}

.booking-cta a {
  color: var(--primary-pink);
  text-decoration: none;
  font-weight: 600;
}

.booking-buttons {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.booking-image {
  height: 500px;
  background: var(--white);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.booking-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  border-radius: 16px;
}

.location-section {
  padding: var(--section-padding);
  background: var(--white);
}

.location-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  margin-top: 60px;
}

.location-info h3 {
  font-family: var(--font-display);
  font-size: 32px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.location-info > p {
  font-size: 18px;
  color: var(--dark-grey);
  line-height: 1.7;
  margin-bottom: 32px;
}

.location-features {
  margin-bottom: 32px;
}

.location-feature {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.location-feature i {
  font-size: 20px;
  color: var(--primary-pink);
}

.location-feature span {
  font-size: 16px;
  color: var(--charcoal);
}

.directions h4 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.directions p {
  font-size: 15px;
  color: var(--dark-grey);
  margin-bottom: 8px;
}

.location-map {
  height: 400px;
  background: var(--soft-pink);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.location-map img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  border-radius: 16px;
}

.faq-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
  margin-top: 60px;
}

.faq-item {
  background: var(--white);
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: var(--transition);
}

.faq-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
}

.faq-item h3 {
  font-family: var(--font-display);
  font-size: 20px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.faq-item p {
  color: var(--dark-grey);
  font-size: 15px;
  line-height: 1.6;
}

/* Footer */
.footer {
  background: var(--charcoal);
  color: var(--white);
  padding: 60px 0 30px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 60px;
  margin-bottom: 40px;
}

.footer-section h3 {
  font-family: var(--font-display);
  font-size: 24px;
  margin-bottom: 20px;
  color: var(--primary-pink);
}

.footer-section p,
.footer-section a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  margin-bottom: 8px;
  display: block;
  transition: var(--transition);
}

.footer-section a:hover {
  color: var(--primary-pink);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 30px;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

/* Responsive */
@media (max-width: 1024px) {
  .hero-content {
    gap: 60px;
  }

  .hero-text h1 {
    font-size: 64px;
  }

  .floating-element-1,
  .floating-element-2 {
    display: none;
  }

  .elements-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  .services-content {
    gap: 60px;
  }

  .about-content {
    gap: 60px;
  }

  .container {
    padding: 0 30px;
  }
}

@media (max-width: 768px) {
  .hero {
    padding-top: 110px; /* Adjust for smaller mobile navbar */
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 50px;
    text-align: center;
  }

  .hero-text::before {
    left: 50%;
    transform: translateX(-50%);
  }

  .hero-text h1 {
    font-size: 52px;
  }

  .hero-stats {
    justify-content: center;
  }

  .hero-visual {
    height: 500px;
  }

  .hero-image-main {
    height: 450px;
    transform: rotate(0deg);
  }

  /* Mobile dropdown positioning */
  .nav-menu.mobile-active {
    top: 110px; /* Match mobile navbar height */
  }

  .elements-grid {
    grid-template-columns: 1fr 1fr;
    gap: 20px;
  }

  .services-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .about-image {
    height: 400px;
  }

  .faq-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  /* Mobile Navigation */
  .nav-menu {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .logo img {
    height: 80px;
    max-width: 250px;
  }

  .floating-element-1,
  .floating-element-2 {
    display: none;
  }

  /* Better mobile spacing */
  .container {
    padding: 0 20px;
  }

  .section-header {
    margin-bottom: 50px;
  }

  .section-title {
    font-size: 36px;
  }

  .section-subtitle {
    font-size: 16px;
  }

  /* Mobile-centered content */
  .services-text,
  .about-text {
    text-align: center;
  }

  .services-text h2,
  .about-text h2 {
    font-size: 36px;
  }

  .services-list {
    text-align: left;
    max-width: 300px;
    margin: 0 auto 40px;
  }
}

@media (max-width: 480px) {
  .hero {
    padding-top: 100px; /* Adjust for smallest mobile navbar */
  }

  .hero-text h1 {
    font-size: 42px;
    line-height: 1.1;
  }

  .hero-text p {
    font-size: 18px;
    margin-bottom: 32px;
  }

  /* Mobile dropdown positioning for small screens */
  .nav-menu.mobile-active {
    top: 100px; /* Match small mobile navbar height */
    padding: 25px 15px;
  }

  .hero-stats {
    flex-direction: column;
    gap: 20px;
    align-items: center;
  }

  .stat-item {
    text-align: center;
  }

  .hero-buttons {
    flex-direction: column;
    gap: 16px;
    width: 100%;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    text-align: center;
    padding: 16px 24px;
  }

  .hero-visual {
    height: 400px;
  }

  .hero-image-main {
    height: 350px;
  }

  .elements-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .element-card {
    padding: 30px 20px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }

  /* Better mobile typography */
  .section-title {
    font-size: 32px;
  }

  .services-text h2,
  .about-text h2 {
    font-size: 32px;
    line-height: 1.2;
  }

  .services-text p,
  .about-text p {
    font-size: 16px;
  }

  /* Mobile navigation improvements */
  .logo img {
    height: 70px;
    max-width: 200px;
  }

  .navbar {
    padding: 12px 0;
  }

  /* Ensure proper mobile spacing */
  .container {
    padding: 0 15px;
  }

  .section-header {
    margin-bottom: 40px;
  }

  /* Mobile-specific element spacing */
  .element-card h3 {
    font-size: 20px;
  }

  .element-card p {
    font-size: 14px;
  }

  /* FAQ mobile improvements */
  .faq-item {
    padding: 24px 20px;
  }

  .faq-item h3 {
    font-size: 18px;
  }

  .faq-item p {
    font-size: 14px;
  }
}

/* Extra small devices (320px and below) */
@media (max-width: 320px) {
  .hero-text h1 {
    font-size: 36px;
  }

  .hero-text p {
    font-size: 16px;
  }

  .section-title {
    font-size: 28px;
  }

  .services-text h2,
  .about-text h2 {
    font-size: 28px;
  }

  .container {
    padding: 0 12px;
  }

  .logo img {
    height: 60px;
    max-width: 180px;
  }

  .btn-primary,
  .btn-secondary {
    padding: 14px 20px;
    font-size: 14px;
  }

  .element-card {
    padding: 24px 16px;
  }

  .faq-item {
    padding: 20px 16px;
  }
}
