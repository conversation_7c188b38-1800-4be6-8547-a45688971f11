// 4 Elements Hair - Premium JavaScript

document.addEventListener("DOMContentLoaded", function () {
  // Smooth scrolling for navigation links
  const navLinks = document.querySelectorAll('a[href^="#"]');
  navLinks.forEach((link) => {
    link.addEventListener("click", function (e) {
      e.preventDefault();
      const targetId = this.getAttribute("href");
      const targetSection = document.querySelector(targetId);

      if (targetSection) {
        const offsetTop = targetSection.offsetTop - 80; // Account for fixed navbar
        window.scrollTo({
          top: offsetTop,
          behavior: "smooth",
        });
      }
    });
  });

  // Navbar scroll effect
  const navbar = document.querySelector(".navbar");
  let lastScrollTop = 0;

  window.addEventListener("scroll", function () {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

    // Add background when scrolled
    if (scrollTop > 50) {
      navbar.style.background = "rgba(255, 255, 255, 0.98)";
      navbar.style.boxShadow = "0 2px 20px rgba(0,0,0,0.1)";
    } else {
      navbar.style.background = "rgba(255, 255, 255, 0.95)";
      navbar.style.boxShadow = "none";
    }

    lastScrollTop = scrollTop;
  });

  // Intersection Observer for animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: "0px 0px -50px 0px",
  };

  const observer = new IntersectionObserver(function (entries) {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = "1";
        entry.target.style.transform = "translateY(0)";
      }
    });
  }, observerOptions);

  // Animate elements on scroll
  const animateElements = document.querySelectorAll(
    ".element-card, .services-text, .services-image"
  );
  animateElements.forEach((el) => {
    el.style.opacity = "0";
    el.style.transform = "translateY(30px)";
    el.style.transition = "opacity 0.6s ease, transform 0.6s ease";
    observer.observe(el);
  });

  // Stagger animation for element cards
  const elementCards = document.querySelectorAll(".element-card");
  elementCards.forEach((card, index) => {
    card.style.transitionDelay = `${index * 0.1}s`;
  });

  // Add hover effects for interactive elements
  const interactiveElements = document.querySelectorAll(
    ".btn-primary, .btn-secondary, .book-btn"
  );
  interactiveElements.forEach((el) => {
    el.addEventListener("mouseenter", function () {
      this.style.transform = "translateY(-2px)";
    });

    el.addEventListener("mouseleave", function () {
      this.style.transform = "translateY(0)";
    });
  });

  // Mobile menu toggle
  const mobileMenuToggle = document.querySelector(".mobile-menu-toggle");
  const navMenu = document.querySelector(".nav-menu");
  const mobileMenuOverlay = document.querySelector(".mobile-menu-overlay");

  function toggleMobileMenu() {
    const isActive = navMenu.classList.contains("mobile-active");

    if (isActive) {
      // Close menu
      navMenu.classList.remove("mobile-active");
      mobileMenuToggle.classList.remove("active");
      mobileMenuOverlay.classList.remove("active");
      document.body.classList.remove("mobile-menu-open");
    } else {
      // Open menu
      navMenu.classList.add("mobile-active");
      mobileMenuToggle.classList.add("active");
      mobileMenuOverlay.classList.add("active");
      document.body.classList.add("mobile-menu-open");
    }
  }

  function closeMobileMenu() {
    navMenu.classList.remove("mobile-active");
    mobileMenuToggle.classList.remove("active");
    mobileMenuOverlay.classList.remove("active");
    document.body.classList.remove("mobile-menu-open");
  }

  if (mobileMenuToggle && navMenu && mobileMenuOverlay) {
    // Toggle menu on hamburger click
    mobileMenuToggle.addEventListener("click", function (e) {
      e.stopPropagation();
      toggleMobileMenu();
    });

    // Close menu when clicking on overlay
    mobileMenuOverlay.addEventListener("click", closeMobileMenu);

    // Close mobile menu when clicking on a link
    const navLinks = navMenu.querySelectorAll(".nav-link, .book-btn");
    navLinks.forEach((link) => {
      link.addEventListener("click", closeMobileMenu);
    });

    // Close menu on escape key
    document.addEventListener("keydown", function (e) {
      if (e.key === "Escape" && navMenu.classList.contains("mobile-active")) {
        closeMobileMenu();
      }
    });

    // Close menu when window is resized to desktop
    window.addEventListener("resize", function () {
      if (
        window.innerWidth > 768 &&
        navMenu.classList.contains("mobile-active")
      ) {
        closeMobileMenu();
      }
    });
  }

  // Parallax effect for hero section
  window.addEventListener("scroll", function () {
    const scrolled = window.pageYOffset;
    const heroImage = document.querySelector(".hero-image");

    if (heroImage) {
      const rate = scrolled * -0.5;
      heroImage.style.transform = `translateY(${rate}px)`;
    }
  });

  // Form validation (if contact form is added later)
  function validateForm(form) {
    const inputs = form.querySelectorAll("input[required], textarea[required]");
    let isValid = true;

    inputs.forEach((input) => {
      if (!input.value.trim()) {
        isValid = false;
        input.classList.add("error");
      } else {
        input.classList.remove("error");
      }
    });

    return isValid;
  }

  // Add loading animation
  function showLoading() {
    const loader = document.createElement("div");
    loader.className = "loader";
    loader.innerHTML = '<div class="spinner"></div>';
    document.body.appendChild(loader);
  }

  function hideLoading() {
    const loader = document.querySelector(".loader");
    if (loader) {
      loader.remove();
    }
  }

  // Initialize AOS (Animate On Scroll) alternative
  function initScrollAnimations() {
    const elements = document.querySelectorAll("[data-animate]");

    elements.forEach((el) => {
      const animationType = el.getAttribute("data-animate");
      el.style.opacity = "0";

      switch (animationType) {
        case "fadeInUp":
          el.style.transform = "translateY(30px)";
          break;
        case "fadeInLeft":
          el.style.transform = "translateX(-30px)";
          break;
        case "fadeInRight":
          el.style.transform = "translateX(30px)";
          break;
        default:
          el.style.transform = "translateY(30px)";
      }

      observer.observe(el);
    });
  }

  // Call initialization functions
  initScrollAnimations();

  // Add smooth page load animation
  window.addEventListener("load", function () {
    document.body.style.opacity = "1";
    document.body.style.transition = "opacity 0.5s ease";
  });

  // Set initial body opacity for load animation
  document.body.style.opacity = "0";
});

// Utility functions
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Optimized scroll handler
const optimizedScrollHandler = debounce(function () {
  // Any scroll-based animations can go here
}, 10);

window.addEventListener("scroll", optimizedScrollHandler);

// Add CSS for loader (injected via JS)
const loaderStyles = `
    .loader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }
    
    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #F4C2C2;
        border-top: 4px solid #2C2C2C;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .error {
        border-color: #e74c3c !important;
        box-shadow: 0 0 5px rgba(231, 76, 60, 0.3) !important;
    }
`;

// Inject loader styles
const styleSheet = document.createElement("style");
styleSheet.textContent = loaderStyles;
document.head.appendChild(styleSheet);
